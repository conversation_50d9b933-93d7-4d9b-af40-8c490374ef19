<?php

use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

// الصفحة الرئيسية للموقع العام
Route::get('/', [App\Http\Controllers\Site\HomepageController::class, 'index'])->name('site.home');

// مسار اختبار للتخطيط الرئيسي للموقع العام
Route::get('/test-layout', function () {
    return view('site.test_layout');
})->name('site.test-layout');

// مسارات السيارات في الموقع العام
Route::prefix('cars')->name('site.cars.')->group(function () {
    Route::get('/', [\Modules\CarCatalog\Http\Controllers\Site\SiteCarController::class, 'index'])->name('index');
    Route::get('/{id}', [\Modules\CarCatalog\Http\Controllers\Site\SiteCarController::class, 'show'])->name('show');
});

// مسار AJAX لجلب الموديلات حسب الماركة في الموقع العام
Route::get('brands/{brand}/models', [\Modules\CarCatalog\Http\Controllers\Site\SiteCarController::class, 'getModelsByBrand'])
    ->name('site.brands.models.get');

Route::prefix('promotions')->name('site.promotions.')->group(function () {
    Route::get('/', function () {
        return view('site.placeholder', ['page_title' => 'عروض السيارات']);
    })->name('index');

    Route::get('/{id}', function ($id) {
        return view('site.promotions.show', compact('id'));
    })->name('show');
});

// مسارات مؤقتة للصفحات المفقودة
Route::get('/contact', function () {
    return view('site.placeholder', ['page_title' => 'اتصل بنا']);
})->name('site.contact');

Route::get('/branches', function () {
    return view('site.placeholder', ['page_title' => 'فروعنا']);
})->name('site.branches');

Route::get('/services', function () {
    return view('site.placeholder', ['page_title' => 'خدماتنا']);
})->name('site.services.index');

Route::get('/corporate', function () {
    return view('site.placeholder', ['page_title' => 'مبيعات الشركات']);
})->name('site.corporate.index');

Route::get('/request-car/step1', function () {
    return view('site.placeholder', ['page_title' => 'اطلب سيارتك']);
})->name('site.request-car.step1');

Route::get('/about', function () {
    return view('site.placeholder', ['page_title' => 'من نحن']);
})->name('site.about');

Route::get('/favorites', function () {
    return view('site.placeholder', ['page_title' => 'المفضلة']);
})->name('site.favorites');

Route::get('/careers', function () {
    return view('site.placeholder', ['page_title' => 'الوظائف']);
})->name('site.careers');

Route::get('/faq', function () {
    return view('site.placeholder', ['page_title' => 'الأسئلة الشائعة']);
})->name('site.faq');

Route::get('/privacy', function () {
    return view('site.placeholder', ['page_title' => 'سياسة الخصوصية']);
})->name('site.privacy');

Route::get('/terms', function () {
    return view('site.placeholder', ['page_title' => 'الشروط والأحكام']);
})->name('site.terms');

Route::get('/support', function () {
    return view('site.placeholder', ['page_title' => 'الدعم الفني']);
})->name('site.support');

// مسار صفحة "كيف أشتريها؟"
Route::get('/how-to-buy', [App\Http\Controllers\Site\InfoPageController::class, 'showHowToBuy'])
    ->name('site.how-to-buy');

// مسارات المصادقة المؤقتة (سيتم استبدالها بمسارات UserManagement module)
Route::get('/login', function () {
    return redirect()->route('site.auth.login.form');
})->name('login');

Route::get('/register', function () {
    return redirect()->route('site.auth.register.form');
})->name('register');

Route::post('/logout', function () {
    auth()->logout();
    return redirect()->route('site.home');
})->name('logout');

// مسارات العملاء المؤقتة
Route::middleware(['auth'])->group(function () {
    Route::get('/customer/dashboard', function () {
        return view('site.placeholder', ['page_title' => 'لوحة تحكم العميل']);
    })->name('customer.dashboard');

    Route::get('/customer/profile', function () {
        return view('site.placeholder', ['page_title' => 'الملف الشخصي']);
    })->name('customer.profile');

    Route::get('/customer/orders', function () {
        return view('site.placeholder', ['page_title' => 'طلباتي']);
    })->name('customer.orders');
});


